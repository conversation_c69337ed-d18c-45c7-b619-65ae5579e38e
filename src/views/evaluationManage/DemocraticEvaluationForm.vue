<!--
 * @Description: 
 * @Autor: panmy
 * @Date: 2025-07-02 14:45:18
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-04 12:00:37
-->
<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" showOkBtn okText="保存" cancelText="关闭" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, computed, toRaw } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseApi } from '@/hooks/web/useBaseApi';
  import { useBaseStore } from '@/store/modules/base';
  import * as schoolApi from '@/api/school';
  import dayjs from 'dayjs';

  const emit = defineEmits(['register', 'reload']);
  const baseStore = useBaseStore();
  // 初始化API和消息提示
  const api = useBaseApi('/api/knsMzpy');
  const { createMessage } = useMessage();

  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    schemas: [
      {
        field: 'dwdm',
        label: '学院',
        component: 'Select',
        componentProps: {
          placeholder: '全部',
          clearImmediate: false,
          onChange: (val, obj) => {
            loadBj(val);
          },
        },
        rules: [{ required: true, trigger: 'blur', message: '请选择学院' }],
      },
      {
        field: 'bjdm',
        label: '班级',
        component: 'Select',
        componentProps: {
          placeholder: '全部',
        },
        rules: [{ required: true, trigger: 'blur', message: '请选择班级' }],
      },
      {
        field: 'xn',
        label: '学年',
        component: 'Select',
        componentProps: {
          placeholder: '全部',
          options: [],
        },
        rules: [{ required: true, trigger: 'blur', message: '请选择学年' }],
      },
      {
        field: 'mzpysj',
        label: '民主评议时间',
        component: 'DatePicker',
        componentProps: { placeholder: '请选择', format: 'YYYY-MM-DD' },
        rules: [{ required: true, trigger: 'blur', message: '请选择民主评议时间' }],
      },
      {
        field: 'cyrs',
        label: '参与人数',
        component: 'InputNumber',
        componentProps: { placeholder: '请输入', min: 0 },
        rules: [{ required: true, trigger: 'blur', message: '请输入参与人数' }],
      },
      {
        field: 'pyzj',
        label: '评议总结',
        component: 'Textarea',
        componentProps: { placeholder: '请输入', rows: 4 },
      },
      {
        field: 'fj',
        label: '附件',
        component: 'UploadFile',
        componentProps: { maxSize: 10 },
      },
    ],
    labelWidth: 120,
  });
  const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(init);
  const id = ref('');
  const getTitle = computed(() => (id.value ? '编辑' : '新建'));

  async function init(data) {
    changeLoading(true);
    resetFields();
    id.value = data?.id || '';
    getOptions();
    if (id.value) {
      try {
        const { data: detailData } = await api.getDetail(id.value);

        // 数据转换处理
        const formData = {
          ...detailData,
          // 日期格式化处理
          mzpysj: detailData.mzpysj ? dayjs(detailData.mzpysj) : undefined,
        };

        setFieldsValue(formData);
      } catch (error) {
        console.error('获取详情失败:', error);
        createMessage.error('获取详情失败');
      }
    }
    changeLoading(false);
  }
  async function handleSubmit() {
    const values = await validate();
    if (!values) return;

    changeOkLoading(true);
    try {
      // 数据转换处理
      const submitData = {
        ...values,
        id: id.value,
        // 日期格式化处理
        mzpysj: values.mzpysj ? dayjs(values.mzpysj).format('YYYY-MM-DD') : '',
        fj: values.fj?.length > 0 ? JSON.stringify(values.fj || '') : '',
      };

      const res = id.value ? await api.edit({ data: submitData }) : await api.save({ data: submitData });

      createMessage.success(res.message || '保存成功');
      closeModal();
      emit('reload');
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    } finally {
      changeOkLoading(false);
    }
  }

  async function getOptions() {
    schoolApi.getXY({ pageSize: 99999 }).then(res => {
      updateSchema({ field: 'dwdm', componentProps: { options: res.data.list, fieldNames: { label: 'dwBzmc', value: 'dwDm' } } });
      setFieldsValue({ zydm: '', nj: '', bjdm: '' });
    });
    const academicYears = await baseStore.getDictionaryData('xn');
    updateSchema({ field: 'xn', componentProps: { options: academicYears, fieldNames: { label: 'fullName', value: 'enCode' } } });
  }
  function loadBj(dwdm) {
    if (!dwdm) return;
    const params = { dwDm: dwdm };
    api.request('get', '/api/public-base/bj/list', { params, isFullPath: true }).then(res => {
      updateSchema({ field: 'bjdm', componentProps: { options: res.data.list, fieldNames: { label: 'bjMc', value: 'bjDm' } } });
    });
  }
</script>
