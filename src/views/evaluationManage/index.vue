<!--
 * @Description: 民主评议管理页面
 * @Autor: Fhz
 * @Date: 2025-02-11 15:30:59
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-04 11:50:23
-->
<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button preIcon="icon-ym icon-ym-btn-add" type="primary" @click="openDemocraticEvaluationFormModal(true, { id: '' })">新建</a-button>
            <a-button preIcon="icon-ym icon-ym-delete" type="error" @click="handleBatchDelete">批量删除</a-button>
            <a-button @click="handleImport" preIcon="icon-ym icon-ym-btn-upload" type="link">导入</a-button>
            <!-- <a-button @click="handleExport" preIcon="icon-ym icon-ym-btn-download" type="link">导出</a-button> -->
            <a-button @click="handleStatistic" preIcon="icon-ym icon-ym-extend-bar-chart" type="link">填写统计</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <DemocraticEvaluationForm @register="registerDemocraticEvaluationFormPopup" @reload="reload" />
    <ImportModal @register="registerImportModal" />
    <StatisticsDrawer :visible="visible" @handleOk="handleOk" />
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { ActionItem, BasicColumn, BasicTable, FormProps, TableAction, useTable } from '@/components/Table';
  import { usePopup } from '@/components/Popup';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseApi } from '@/hooks/web/useBaseApi';
  import DemocraticEvaluationForm from './DemocraticEvaluationForm.vue';
  import StatisticsDrawer from './StatisticsDrawer.vue';
  import ImportModal from '@/components/CommonModal/src/ImportModal.vue';
  import dayjs from 'dayjs';

  // 初始化API
  const api = useBaseApi('/api/knsMzpy');
  const { createMessage, createConfirm } = useMessage();

  const [registerTable, { reload, getSelectRowKeys }] = useTable({
    api: params => api.getList({ params }),
    useSearchForm: true,
    rowSelection: {
      type: 'checkbox',
    },
    columns: [
      { title: '院系', dataIndex: 'dwdm', width: 150 },
      { title: '班级', dataIndex: 'bjdm', width: 150 },
      { title: '学年', dataIndex: 'xn', width: 120 },
      { title: '上报人姓名', dataIndex: 'sbrxm', width: 120 },
      { title: '上报人工号', dataIndex: 'sbrgh', width: 120 },
      { title: '民主评议时间', dataIndex: 'mzpysj', width: 150, format: 'date|YYYY-MM-DD' },
      { title: '参与人数', dataIndex: 'cyrs', width: 100 },
      { title: '单位类型', dataIndex: 'dwlx', width: 100 },
    ],
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
    },
    formConfig: {
      schemas: [
        {
          field: 'keyword',
          label: '关键字',
          component: 'Input',
          componentProps: {
            placeholder: '请输入上报人姓名/上报人工号',
            submitOnPressEnter: true,
          },
        },
        {
          field: 'dwdm',
          label: '院系',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            options: [],
          },
        },
        {
          field: 'xn',
          label: '学年',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            options: [],
          },
        },
      ],
    },
  });
  const [registerDemocraticEvaluationFormPopup, { openModal: openDemocraticEvaluationFormModal }] = useModal();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  function getTableActions(record): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: () => {
          openDemocraticEvaluationFormModal(true, {
            id: record.id,
          });
        },
      },
      {
        label: '删除',
        color: 'error',
        modelConfirm: {
          onOk: () => handleDelete(record.id),
        },
      },
    ];
  }
  // 单条删除
  async function handleDelete(id: string | number) {
    try {
      const { msg } = await api.remove(id);
      createMessage.success(msg || '删除成功');
      reload();
    } catch (error) {
      console.error('删除失败:', error);
      createMessage.error('删除失败');
    }
  }

  // 批量删除
  function handleBatchDelete() {
    const ids = getSelectRowKeys();
    if (!ids || ids.length === 0) {
      createMessage.warning('请选择要删除的记录');
      return;
    }
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: `确认要批量删除选中的 ${ids.length} 条记录吗？`,
      onOk: async () => {
        try {
          const { msg } = await api.batchRemove({ params: { ids: ids.join(',') } });
          createMessage.success(msg || '批量删除成功');
          reload();
        } catch (error) {
          console.error('批量删除失败:', error);
          createMessage.error('批量删除失败');
        }
      },
    });
  }

  function handleImport() {
    openImportModal(true, {
      type: 'position',
      isUploadDirectly: true,
      actionUrl: '/api/knsMzpy/import',
      queryObj,
      downloadTemplateUrl: '/api/knsMzpy/downloadTemplate',
    });
  }

  // 导出
  async function handleExport() {
    const listQuery = getFetchParams();
    openExportModal(true, { listQuery, exportType: 'KnsDcwjPersonImport', apiUrl: '/api/knsDcwjPerson/export' });
  }

  const visible = ref(false);
  function handleStatistic() {
    visible.value = true;
  }

  function handleOk() {
    visible.value = false;
    reload();
  }
</script>
